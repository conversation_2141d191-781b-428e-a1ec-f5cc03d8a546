<template>
  <div v-if="currentTheme" class="message-input" @click="handleContainerClick">
    <div class="editor-wrapper">
      <textarea ref="textareaRef" v-model="content" :placeholder="placeholder" :disabled="disabled"
        :maxlength="maxLength" class="textarea" @keydown="handleKeydown" @input="handleInput" @focus="handleFocus"
        @blur="handleBlur"></textarea>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui'
import { computed, onMounted, ref, watch } from 'vue'
import { useNoteMessagesStore } from '../stores/noteMessages'
import { useNoteThemesStore } from '../stores/noteThemes'

// Props
interface Props {
  placeholder?: string
  disabled?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入笔记内容...',
  disabled: false,
  maxLength: 5000
})

// Emits
interface Emits {
  (e: 'message-sent', content: string): void
}

const emit = defineEmits<Emits>()

const message = useMessage()
const themesStore = useNoteThemesStore()
const messagesStore = useNoteMessagesStore()

// 引用
const textareaRef = ref<HTMLTextAreaElement>()

// 状态
const content = ref('')
const isFocused = ref(false)

// 计算属性
const currentTheme = computed(() => themesStore.currentTheme)

// 处理输入
const handleInput = () => {
  // 内容已通过 v-model 自动更新
}

// 处理焦点
const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  isFocused.value = false
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  console.log('🎹 检测到按键:', {
    key: event.key,
    metaKey: event.metaKey,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey
  })

  if (event.key === 'Enter') {
    if (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) {
      // Cmd+Enter、Ctrl+Enter、Shift+Enter、Option+Enter 允许换行
      console.log('🔥 检测到换行快捷键')
      // 不阻止默认行为，允许换行
      return
    } else {
      // 单独的 Enter 键发送消息
      console.log('🔥 检测到 Enter 键，准备发送消息')
      event.preventDefault()
      event.stopPropagation()
      handleSendMessage()
    }
  }
}

// 处理发送消息
const handleSendMessage = () => {
  console.log('📤 MessageInput.handleSendMessage 开始执行')
  console.log('📤 content.value:', content.value)
  console.log('📤 content.trim():', content.value.trim())
  console.log('📤 props.disabled:', props.disabled)

  if (!content.value.trim() || props.disabled) {
    console.log('❌ 发送被阻止 - content.trim():', !!content.value.trim(), 'disabled:', props.disabled)
    return
  }

  console.log('📤 准备调用 handleSend')
  handleSend(content.value.trim())
}

// 处理发送消息（原有的业务逻辑）
const handleSend = async (messageContent: string) => {
  console.log('🚀 MessageInput.handleSend 开始执行')
  console.log('🚀 接收到的内容:', messageContent)
  console.log('🚀 内容长度:', messageContent?.length || 0)
  console.log('🚀 content.trim():', messageContent?.trim())
  console.log('🚀 currentTheme.value:', currentTheme.value)
  console.log('🚀 props.disabled:', props.disabled)

  if (!messageContent.trim() || !currentTheme.value || props.disabled) {
    console.log('❌ 发送被阻止 - content.trim():', !!messageContent?.trim(), 'currentTheme:', !!currentTheme.value, 'disabled:', props.disabled)
    return
  }

  console.log('✅ 验证通过，开始发送消息')

  try {
    console.log('📡 准备调用 messagesStore.createMessageOnServer')
    console.log('📡 参数 - themeId:', currentTheme.value.id, 'content:', messageContent, 'type: text')

    // 创建消息
    await messagesStore.createMessageOnServer(
      currentTheme.value.id,
      messageContent,
      'text'
    )

    console.log('✅ 消息创建成功')

    // 清空编辑器
    console.log('🧹 准备清空编辑器')
    clearContent()
    console.log('✅ 编辑器已清空')

    // 聚焦编辑器
    console.log('🎯 准备聚焦编辑器')
    setTimeout(() => {
      focus()
      console.log('✅ 编辑器已聚焦')
    }, 100)

    // 触发事件
    console.log('📢 准备触发 message-sent 事件')
    emit('message-sent', messageContent)
    console.log('✅ message-sent 事件已触发')

    console.log('🎉 准备显示成功消息')
    message.success('消息发送成功')
    console.log('✅ 发送流程完成')
  } catch (error) {
    console.error('❌ 发送消息失败:', error)
    console.error('❌ 错误详情:', {
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      name: (error as any)?.name
    })
    message.error('发送消息失败')
  }
}

// 处理容器点击事件
const handleContainerClick = (event: Event) => {
  // 如果点击的不是文本域本身，则聚焦编辑器
  const target = event.target as HTMLElement
  if (!target.closest('textarea')) {
    focus()
  }
}

// 聚焦编辑器
const focus = () => {
  if (textareaRef.value) {
    textareaRef.value.focus()
  }
}

// 获取当前内容
const getContent = (): string => {
  return content.value
}

// 设置内容
const setContent = (newContent: string) => {
  content.value = newContent
}

// 清空内容
const clearContent = () => {
  content.value = ''
}

// 监听禁用状态
watch(() => props.disabled, (disabled) => {
  if (disabled && textareaRef.value) {
    textareaRef.value.blur()
  }
})

// 生命周期
onMounted(() => {
  // 组件挂载后聚焦
  focus()
})

// 暴露方法给父组件
defineExpose({
  focus,
  getContent,
  setContent,
  clearContent
})
</script>

<style scoped>
.message-input {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px 8px;
  /* background-color: var(--n-color); */
  /* border-top: 1px solid var(--n-border-color); */
  box-sizing: border-box;
  cursor: text;
}

.editor-wrapper {
  flex: 1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.textarea {
  width: 100%;
  height: 100%;
  /* min-height: 60px; */
  padding-bottom: 100px;
  color: var(--n-text-color);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  border: none;
  font-family: inherit;
  box-sizing: border-box;
  background: transparent;
}

.textarea:disabled {
  cursor: not-allowed;
}

/* 滚动条样式 */
.textarea::-webkit-scrollbar {
  width: 6px;
}

.textarea::-webkit-scrollbar-track {
  background: transparent;
}

.input-hint {
  flex-shrink: 0;
  margin-top: 8px;
  text-align: center;
  padding: 4px 0;
}

.hint-text {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.hint-text kbd {
  background-color: var(--n-color-embedded);
  border: 1px solid var(--n-border-color);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: inherit;
  color: var(--n-text-color-2);
  margin: 0 2px;
}
</style>
